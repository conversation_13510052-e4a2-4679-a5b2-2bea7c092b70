{"name": "airspace-intelligence-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@material-tailwind/react": "^2.1.10", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@tailwindcss/vite": "^4.1.8", "@turf/turf": "^7.2.0", "@types/leaflet": "^1.9.18", "@types/plotly.js": "^3.0.3", "@types/react-leaflet": "^2.8.3", "date-fns": "^3.6.0", "framer-motion": "^12.18.1", "leaflet": "^1.9.4", "lucide-react": "^0.513.0", "plotly.js-dist-min": "^3.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-leaflet": "^5.0.0", "react-plotly.js": "^2.6.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "tailwindcss": "^4.1.8", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}