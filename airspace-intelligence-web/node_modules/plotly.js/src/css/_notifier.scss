.plotly-notifier {
    font-family: 'Open Sans', verdana, arial, sans-serif; // Because not all contexts have this specified.
    position: fixed;
    top: 50px;
    right: 20px;
    z-index: 10000;
    font-size: 10pt;
    max-width: 180px;

    p {
        margin: 0;
    }

    .notifier-note {
        min-width: 180px;
        max-width: 250px;
        border: 1px solid $color-bg-light;
        z-index: 3000;
        margin: 0;

        background-color: #8c97af;
        background-color: rgba(140, 151, 175, 0.9);
        color: $color-bg-light;
        padding: 10px;

        overflow-wrap: break-word;
        word-wrap: break-word;

        -ms-hyphens: auto;
        -webkit-hyphens: auto;
        hyphens: auto;
    }

    .notifier-close {
        color: $color-bg-light;
        opacity: 0.8;

        float: right;
        padding: 0 5px;

        background: none;
        border: none;
        font-size: 20px;
        font-weight: bold;
        line-height: 20px;

        &:hover {
            color: #444;
            text-decoration: none;
            cursor: pointer;
        }
    }
}
