import { API_ENDPOINTS } from "../utils/constants";
import type { WeatherFeature } from "../types/weather.types";

export class WeatherDataService {
    private static instance: WeatherDataService;

    public static getInstance(): WeatherDataService {
        if (!WeatherDataService.instance) {
            WeatherDataService.instance = new WeatherDataService();
        }
        return WeatherDataService.instance;
    }
    async fetchWeatherData(): Promise<{ features: WeatherFeature[] }> {
        try {
            const response = await fetch(API_ENDPOINTS.wfs)
            if (!response.ok) {
                throw new Error(`HTTP error status: ${response.status}`)
            }
            return await response.json();
        } catch (error) {
            console.error('Error fecthing weather data:', error);
            throw error;
        }
    }


    categorizeFeatures(features: WeatherFeature[]): {
        sigmetFeatures: WeatherFeature[];
        airmetFeatures: WeatherFeature[];
    } {
        const sigmetFeatures: WeatherFeature[] = [];
        const airmetFeatures: WeatherFeature[] = [];

        features.forEach(feature => {
            const type = feature.properties?.types?.toLowerCase();
            if (type?.includes('sigmet')) {
                sigmetFeatures.push(feature);
            } else if (type?.includes('airmet')) {
                airmetFeatures.push(feature);
            }
        });

        return { sigmetFeatures, airmetFeatures };
    }
}
