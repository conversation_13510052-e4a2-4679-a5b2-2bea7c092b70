import { useState } from 'react';
import { AirspaceMap } from './components/Map/AirspaceMap';
import { FilterControls } from './components/UI/FilterControls';
import { ChartPopup } from './components/Charts/ChartPopup';
import type { LayerVisibility, MapClickEvent, SelectedElement, TimeRange, FilterState } from './types';
import './App.css';

function App() {
  const [mapView, setMapView] = useState<L.LatLng | null>(null);
  const [mapGeo, setMapGeo] = useState<GeoJSON.GeoJsonObject | null>(null);
  const [selectedElement, setSelectedElement] = useState<SelectedElement | null>(null);

  const [layerVisibility, setLayerVisibility] = useState<LayerVisibility>({
    sigmetLayer: false, // Start with weather hidden
    airmetLayer: false,
    flightLayer: true,
    dangerAreaLayer: true,
    restrictedAreaLayer: true,
    militaryAreaLayer: true,
    airportLayer: true,
    sectorLayer: false, // Start with sectors hidden for cleaner view
    waypointLayer: true
  });

  const [timeRange, setTimeRange] = useState<TimeRange>({
    start: new Date().toISOString(),
    end: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    current: new Date().toISOString(),
    offset: 0
  });

  const [filters, setFilters] = useState<FilterState>({
    flight: {},
    airspace: { activeOnly: true },
    weather: { types: ['sigmet', 'airmet'], severityLevels: [] }
  });

  const [showChartPopup, setShowChartPopup] = useState(false);
  const [chartPopupPosition, setChartPopupPosition] = useState({ x: 0, y: 0 });

  const handleMapClick = (event: MapClickEvent) => {
    console.log('Map clicked:', event);

    if (event.elementType && event.elementId) {
      const element = {
        type: event.elementType,
        id: event.elementId,
        data: event.elementData,
        coordinates: event.coordinates
      };

      setSelectedElement(element);

      // Show chart popup for clickable elements
      if (['airport', 'sector', 'waypoint'].includes(event.elementType)) {
        let x = 100;
        let y = 100;

        // Use screen coordinates if available (from clicked element)
        if (event.screenCoordinates) {
          x = event.screenCoordinates.x;
          y = event.screenCoordinates.y;

          // Offset popup to avoid covering the clicked element
          x += 20;
          y -= 150; // Position above the clicked element
        }

        // Ensure popup stays within screen bounds
        const popupWidth = 400;
        const popupHeight = 300;
        const margin = 20;

        // Adjust horizontal position
        if (x + popupWidth > window.innerWidth - margin) {
          x = window.innerWidth - popupWidth - margin;
        }
        if (x < margin) {
          x = margin;
        }

        // Adjust vertical position
        if (y + popupHeight > window.innerHeight - margin) {
          y = window.innerHeight - popupHeight - margin;
        }
        if (y < margin) {
          y = margin;
        }

        setChartPopupPosition({ x, y });

        // Use setTimeout to ensure smooth animation
        setTimeout(() => {
          setShowChartPopup(true);
        }, 50);
      }
    } else {
      setSelectedElement(null);
      setShowChartPopup(false);
    }
  };

  return (
    <div className="w-screen h-screen relative bg-gray-900 overflow-hidden">
      <AirspaceMap
        layerVisibility={layerVisibility}
        mapView={mapView}
        mapGeo={mapGeo}
        onMapClick={handleMapClick}
        selectedElement={selectedElement}
      />

      <FilterControls
        layerVisibility={layerVisibility}
        setLayerVisibility={setLayerVisibility}
        timeRange={timeRange}
        setTimeRange={setTimeRange}
        filters={filters}
        setFilters={setFilters}
      />

      {/* Selected Element Info */}
      {selectedElement && !showChartPopup && (
        <div className="absolute top-4 right-4 bg-slate-800/95 backdrop-blur-md border-2 border-amber-400/30 text-white p-3 rounded-xl z-10 max-w-xs shadow-xl">
          <h4 className="text-sm font-bold mb-2 text-amber-200">Selected Element</h4>
          <div className="space-y-1 text-xs">
            <p><strong className="text-amber-300">{selectedElement.type.charAt(0).toUpperCase() + selectedElement.type.slice(1)}:</strong> <span className="text-white">{selectedElement.id}</span></p>
            <p className="text-gray-300">
              {selectedElement.coordinates[0].toFixed(4)}, {selectedElement.coordinates[1].toFixed(4)}
            </p>
            {['airport', 'sector', 'waypoint'].includes(selectedElement.type) && (
              <div className="mt-2 p-2 bg-blue-500/20 border border-blue-400/30 rounded-md">
                <p className="text-blue-300 text-xs">🔍 Click for detailed analysis</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Chart Popup */}
      {showChartPopup && selectedElement && (
        <ChartPopup
          isOpen={showChartPopup}
          onClose={() => setShowChartPopup(false)}
          selectedElement={selectedElement}
          position={chartPopupPosition}
        />
      )}
    </div>
  );
}

export default App;
