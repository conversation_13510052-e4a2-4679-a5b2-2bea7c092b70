import { motion, AnimatePresence } from 'framer-motion';
import { useState } from 'react';
import { useWeatherData } from '../../hooks/useWeatherData';
import { TimeCalculator } from '../../utils/timeUtils';
import L from 'leaflet';
import { FIR_NAME } from '../../utils/constants';
import type { GeoJsonObject } from 'geojson';

interface StatusCardProps {
    setMapView: (mapView: L.LatLng) => void;
    setMapGeo: (mapGeo: GeoJsonObject) => void;
    layerVisibility: {
        sigmetLayer: boolean;
        airmetLayer: boolean;
    };
}

export function StatusCard({
    layerVisibility: layerVisibility,
    setMapView: setMapView,
    setMapGeo: setMapGeo
}: StatusCardProps) {
    const [statusToggle, setStatusToggle] = useState(false)
    const { sigmetFeatures, airmetFeatures } = useWeatherData();
    const handleFeatureHover = (feature: any) => {
        const layer = L.geoJSON(feature);
        const center = layer.getBounds().getCenter();
        setMapView(center);
        setMapGeo(feature)
    };

    return (
        // Cover Animation container
        <AnimatePresence>
            {/* Animation Parent Div */}
            <motion.div className="
                absolute 
                flex flex-col 
                top-10 right-10 
                rounded-[15px]
                bg-white/10  
                ring-1 ring-white/20    
                min-h-[110px]
                min-w-0 
                max-h-9/10
                backdrop-blur-xs
                gap-y-3
                p-[10px]
                items-center
                "
                style={{
                    boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)',
                    border: '1px solid rgba(255, 255, 255, 0.18)',
                    borderTop: '1px solid rgba( 255, 255, 255, 0.4 )',
                    borderRight: '1px solid rgba( 255, 255, 255, 0.4 )',
                    zIndex: 4
                }}
                animate={{
                    height: statusToggle ? 'auto' : '110px'
                }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
            >
                {/* STATUS DIV */}
                <motion.div className={`
                flex flex-col
                rounded-[10px]
                max-h-[15vh]
                px-[10px]
                cursor-pointer
                `}
                    onClick={() => setStatusToggle(prev => !prev)}
                    initial={false}
                    animate={{
                        backgroundColor: statusToggle ? 'rgba(0,0,0,0.4)' : 'rgba(0,0,0,0)',
                        boxShadow: statusToggle ? '0 0 0 1px rgba(255,255,255,0.2)' : 'none',
                        width: statusToggle ? '317px' : '260px',

                    }}

                    transition={{ duration: 0.3, ease: "easeOut" }}
                >

                    <div><span className="font-semibold">STATUS</span></div>
                    <div className="flex gap-x-[50px] justify-center font-thin pb-[10px] overflow-auto">
                        <div className="flex flex-col gap-1">
                            <div className="text-[2.7vh]">{sigmetFeatures.length}</div>
                            <div className="flex">
                                <div className="grow text-[1.8vh]">SIGMETs</div>
                                <div className="rounded-full size-[8px] bg-green-500 blur-[1.5px]"></div>
                            </div>
                        </div>
                        <div className="flex flex-col gap-1">
                            <div className="text-[2.7vh]">{airmetFeatures.length}</div>
                            <div className="flex">
                                <div className="grow text-[1.8vh]">AIRMETs</div>
                                <div className="rounded-full size-[8px] bg-orange-500 blur-[1.5px]"></div>
                            </div>
                        </div>

                    </div>
                    {/* <span className="font-light text-[10px]">Expand</span> */}
                </motion.div>

                {statusToggle && (
                    <>
                        {/* This is Sub-container for Sigmet visualization */}
                        {layerVisibility.sigmetLayer && (
                            <div className="
                                flex flex-col
                                bg-black/40
                                rounded-[10px]
                                ring-1 ring-white/20
                                w-auto 
                                min-h-0
                                max-h-[50vh]
                                gap-2
                                px-[10px]
                                "
                            >
                                <div className="pt-[1.5vh] font-bold"><span>SIGMET</span></div>
                                <div className="flex flex-col overflow-y-auto min-h-0 p-[10px] gap-4 custom-scrollbar-left">

                                    {/* This loop the feature to feeding up the informations  */} {/*Add no data div*/}
                                    {sigmetFeatures.map((feature) => (
                                        <div className="
                                            flex flex-col
                                            bg-white/10
                                            rounded-[10px]
                                            ring-1 ring-white/20
                                            w-full max-h-[350px]
                                            backdrop-blur-xs
                                            items-center
                                            "
                                            key={feature.properties.item_id}
                                            onMouseEnter={() => handleFeatureHover(feature)}
                                        >

                                            {/* This div using to show flag and FIR Name */}
                                            <div className="flex gap-x-2 pt-[3px] items-center">
                                                <img src={`https://flagsapi.com/${FIR_NAME[feature.properties.locations as keyof typeof FIR_NAME]}/flat/32.png`} className="rounded-[14px]" />
                                                <span className="text-[15px] font-light">
                                                    {feature.properties.locations}
                                                </span>
                                            </div>


                                            {/* This div use to show Lower and Upper MSL Level and events icon */}
                                            <div className="flex gap-x-[53px] items-center">
                                                <div className="flex flex-col ">
                                                    <span className="text-[10px]">Lower</span>
                                                    <span className="text-[13px] font-bold">{feature.properties.lower}</span>
                                                    <span className="text-[10px]">MSL</span>
                                                </div>
                                                <div >
                                                    <img src="/src/assets/icon/Group 18.png" className="w-9 h-9" />

                                                </div>
                                                <div className="flex flex-col">
                                                    <span className="text-[10px]">Upper</span>
                                                    <span className="text-[13px] font-bold">{feature.properties.upper}</span>
                                                    <span className="text-[10px]">MSL</span>
                                                </div>
                                            </div>

                                            {/* This div use to show the Phenomenon text*/}
                                            <div>
                                                <span className="text-[20px] font-semibold">{feature.properties.hazard}</span>
                                            </div>

                                            {/* This div control the progression bar */}
                                            <div className="flex gap-x-1 items-center">
                                                <div className="text-[10px] font-bold">Begins</div>
                                                <div className="w-[7vw] h-[0.75vh] bg-gray-200 rounded-full ">
                                                    <div
                                                        className="bg-teal-500 h-[0.75vh] rounded-full transition-all duration-500 ease-in-out"
                                                        style={{ width: `${37}%` }}
                                                    ></div>
                                                </div>
                                                <div className="text-[10px] font-bold">Ends</div>
                                            </div>

                                            {/* This div use to show time remaining */}
                                            <div className="flex gap-x-[10px] px-[10px] pb-[2px]">
                                                <div className="text-[10px] font-light">{feature.properties.valid_start}</div>
                                                <div className="text-[10px] font-light italic">{TimeCalculator.calculateTimeRemaining(feature.properties.valid_end)}</div>
                                                <div className="text-[10px] font-light">{feature.properties.valid_end}</div>
                                            </div>


                                        </div>
                                    ))}
                                </div>
                            </div>
                        )
                        }

                        {/* This is Sub-container for Sigmet visualization */}
                        {layerVisibility.airmetLayer && (
                            <div className="
                                flex flex-col
                                bg-black/40
                                rounded-[10px]
                                ring-1 ring-white/20
                                w-full
                                min-h-0
                                max-h-[50vh]
                                gap-2
                                px-[10px]
                                "
                            // initial={{ height: 0 }}
                            // animate={{
                            //     height: '100%'
                            // }}
                            // exit={{ height: 0 }}
                            // transition={{ duration: 0.3, ease: 'easeInOut' }}
                            >
                                <div className="pt-[1.5vh]"><span><strong>AIRMET</strong></span></div>
                                <div className="flex flex-col overflow-y-auto min-h-0 p-[10px] gap-4 custom-scrollbar-left">

                                    {/* This loop the feature to feeding up the informations */}{/*Add no data div*/}
                                    {airmetFeatures.map((feature) => (
                                        <div className="
                                            flex flex-col
                                            bg-white/10
                                            rounded-[10px]
                                            ring-1 ring-white/20
                                            w-auto max-h-[350px]
                                            backdrop-blur-xs
                                            items-center
                                            "
                                            key={feature.properties.item_id}
                                            onMouseEnter={() => handleFeatureHover(feature)}
                                        >

                                            {/* This div using to show flag and FIR Name */}
                                            <div className="flex gap-x-2 pt-[3px] items-center">
                                                <img src="https://flagsapi.com/AU/flat/32.png" className="rounded-[14px]" />
                                                <span className="text-[15px] font-light">{feature.properties.locations}</span>
                                            </div>


                                            {/* This div use to show Lower and Upper MSL Level and events icon */}
                                            <div className="flex gap-x-[53px] items-center">
                                                <div className="flex flex-col ">
                                                    <span className="text-[10px]">Lower</span>
                                                    <span className="text-[13px] font-bold">{feature.properties.lower}</span>
                                                    <span className="text-[10px]">MSL</span>
                                                </div>
                                                <div >
                                                    <img src="/src/assets/icon/Group 18.png" className="w-9 h-9" />

                                                </div>
                                                <div className="flex flex-col">
                                                    <span className="text-[10px]">Upper</span>
                                                    <span className="text-[13px] font-bold">{feature.properties.upper}</span>
                                                    <span className="text-[10px]">MSL</span>
                                                </div>
                                            </div>

                                            {/* This div use to show the Phenomenon text*/}
                                            <div>
                                                <span className="text-[20px] font-semibold">{feature.properties.hazard}</span>
                                            </div>

                                            {/* This div control the progression bar */}
                                            <div className="flex gap-x-1 items-center">
                                                <div className="text-[10px] font-bold">Begins</div>
                                                <div className="w-[7vw] h-[0.75vh] bg-gray-200 rounded-full ">
                                                    <div
                                                        className="bg-teal-500 h-[0.75vh] rounded-full transition-all duration-500 ease-in-out"
                                                        style={{ width: `${37}%` }}
                                                    ></div>
                                                </div>
                                                <div className="text-[10px] font-bold">Ends</div>
                                            </div>

                                            {/* This div use to show time remaining */}
                                            <div className="flex gap-x-[10px] px-[10px] pb-[2px]">
                                                <div className="text-[10px] font-light">{feature.properties.valid_start}</div>
                                                <div className="text-[10px] font-light italic">{TimeCalculator.calculateTimeRemaining(feature.properties.valid_end)}</div>
                                                <div className="text-[10px] font-light">{feature.properties.valid_end}</div>
                                            </div>


                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </>

                )}

            </motion.div>
        </AnimatePresence >
    )
}