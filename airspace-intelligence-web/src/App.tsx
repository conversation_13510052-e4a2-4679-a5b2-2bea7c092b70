import { useState } from 'react';
import { AirspaceMap } from './components/Map/AirspaceMap';
import { FilterControls } from './components/UI/FilterControls';
import { ChartPopup } from './components/Charts/ChartPopup';
import type { LayerVisibility, MapClickEvent, SelectedElement, TimeRange, FilterState } from './types';
import './App.css';

function App() {
  const [mapView, setMapView] = useState<L.LatLng | null>(null);
  const [mapGeo, setMapGeo] = useState<GeoJSON.GeoJsonObject | null>(null);
  const [selectedElement, setSelectedElement] = useState<SelectedElement | null>(null);

  const [layerVisibility, setLayerVisibility] = useState<LayerVisibility>({
    sigmetLayer: false, // Start with weather hidden
    airmetLayer: false,
    flightLayer: true,
    dangerAreaLayer: true,
    restrictedAreaLayer: true,
    militaryAreaLayer: true,
    airportLayer: true,
    sectorLayer: false, // Start with sectors hidden for cleaner view
    waypointLayer: true
  });

  const [timeRange, setTimeRange] = useState<TimeRange>({
    start: new Date().toISOString(),
    end: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    current: new Date().toISOString(),
    offset: 0
  });

  const [filters, setFilters] = useState<FilterState>({
    flight: {},
    airspace: { activeOnly: true },
    weather: { types: ['sigmet', 'airmet'], severityLevels: [] }
  });

  const [showChartPopup, setShowChartPopup] = useState(false);
  const [chartPopupPosition, setChartPopupPosition] = useState({ x: 0, y: 0 });

  const handleMapClick = (event: MapClickEvent) => {
    console.log('Map clicked:', event);

    if (event.elementType && event.elementId) {
      const element = {
        type: event.elementType,
        id: event.elementId,
        data: event.elementData,
        coordinates: event.coordinates
      };

      setSelectedElement(element);

      // Show chart popup for clickable elements
      if (['airport', 'sector', 'waypoint'].includes(event.elementType)) {
        // Position popup intelligently based on screen space
        const mapElement = document.getElementById('map');
        if (mapElement) {
          const rect = mapElement.getBoundingClientRect();
          const centerX = rect.width / 2;
          const centerY = rect.height / 2;

          // Position popup away from edges and filter controls
          let x = centerX + 100;
          let y = centerY - 150;

          // Adjust if too close to edges
          if (x + 400 > window.innerWidth - 20) {
            x = window.innerWidth - 420;
          }
          if (x < 320) { // Account for filter controls
            x = 320;
          }
          if (y < 20) {
            y = 20;
          }
          if (y + 300 > window.innerHeight - 20) {
            y = window.innerHeight - 320;
          }

          setChartPopupPosition({ x, y });

          // Use setTimeout to ensure smooth animation
          setTimeout(() => {
            setShowChartPopup(true);
          }, 50);
        }
      }
    } else {
      setSelectedElement(null);
      setShowChartPopup(false);
    }
  };

  return (
    <div className="w-screen h-screen relative bg-gray-900 overflow-hidden">
      <AirspaceMap
        layerVisibility={layerVisibility}
        mapView={mapView}
        mapGeo={mapGeo}
        onMapClick={handleMapClick}
        selectedElement={selectedElement}
      />

      <FilterControls
        layerVisibility={layerVisibility}
        setLayerVisibility={setLayerVisibility}
        timeRange={timeRange}
        setTimeRange={setTimeRange}
        filters={filters}
        setFilters={setFilters}
      />

      {/* Selected Element Info */}
      {selectedElement && !showChartPopup && (
        <div className="absolute top-4 right-4 bg-slate-800/95 backdrop-blur-md border-2 border-amber-400/30 text-white p-3 rounded-xl z-10 max-w-xs shadow-xl">
          <h4 className="text-sm font-bold mb-2 text-amber-200">Selected Element</h4>
          <div className="space-y-1 text-xs">
            <p><strong className="text-amber-300">{selectedElement.type.charAt(0).toUpperCase() + selectedElement.type.slice(1)}:</strong> <span className="text-white">{selectedElement.id}</span></p>
            <p className="text-gray-300">
              {selectedElement.coordinates[0].toFixed(4)}, {selectedElement.coordinates[1].toFixed(4)}
            </p>
            {['airport', 'sector', 'waypoint'].includes(selectedElement.type) && (
              <div className="mt-2 p-2 bg-blue-500/20 border border-blue-400/30 rounded-md">
                <p className="text-blue-300 text-xs">🔍 Click for detailed analysis</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Chart Popup */}
      {showChartPopup && selectedElement && (
        <ChartPopup
          isOpen={showChartPopup}
          onClose={() => setShowChartPopup(false)}
          selectedElement={selectedElement}
          position={chartPopupPosition}
        />
      )}
    </div>
  );
}

export default App;
