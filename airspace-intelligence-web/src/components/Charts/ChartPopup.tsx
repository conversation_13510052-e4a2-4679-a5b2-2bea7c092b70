import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, BarChart3, TrendingUp } from 'lucide-react';
import { DemandCapacityChart } from './DemandCapacityChart';
import { FlightDataService } from '../../services/FlightDataService';
import type { ChartData, SelectedElement } from '../../types';

interface ChartPopupProps {
  isOpen: boolean;
  onClose: () => void;
  selectedElement: SelectedElement;
  position: { x: number; y: number };
}

export const ChartPopup: React.FC<ChartPopupProps> = ({
  isOpen,
  onClose,
  selectedElement,
  position
}) => {
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState(false);

  // Generate chart data when popup opens
  useEffect(() => {
    if (!isOpen) return;

    const generateChartData = async () => {
      setLoading(true);
      try {
        const flightService = FlightDataService.getInstance();
        const newData = flightService.generateDemandCapacityData(
          selectedElement.id,
          selectedElement.type as 'airport' | 'sector' | 'waypoint'
        );

        const newChartData: ChartData = {
          type: 'demand-capacity',
          title: `${selectedElement.type.charAt(0).toUpperCase() + selectedElement.type.slice(1)} - ${selectedElement.id}`,
          timeRange: {
            start: new Date().toISOString(),
            end: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          },
          data: newData,
          metadata: selectedElement.data?.capacity ? {
            capacity: selectedElement.data.capacity.totalHourly || selectedElement.data.controllerCapacity || 50,
            currentDemand: selectedElement.data.capacity?.currentDemand || selectedElement.data.currentTraffic || 30,
            utilizationPercentage: selectedElement.data.capacity?.utilizationPercentage || selectedElement.data.utilizationPercentage || 60
          } : {
            capacity: 50,
            currentDemand: 30,
            utilizationPercentage: 60
          }
        };

        setChartData(newChartData);
      } catch (error) {
        console.error('Error generating chart data:', error);
      } finally {
        setLoading(false);
      }
    };

    generateChartData();
  }, [isOpen, selectedElement]);

  if (!isOpen) return null;

  // Calculate popup position to keep it on screen
  const popupWidth = 400;
  const popupHeight = 300;
  const margin = 20;
  
  let adjustedX = position.x;
  let adjustedY = position.y;
  
  // Adjust horizontal position
  if (adjustedX + popupWidth > window.innerWidth - margin) {
    adjustedX = window.innerWidth - popupWidth - margin;
  }
  if (adjustedX < margin) {
    adjustedX = margin;
  }
  
  // Adjust vertical position
  if (adjustedY + popupHeight > window.innerHeight - margin) {
    adjustedY = window.innerHeight - popupHeight - margin;
  }
  if (adjustedY < margin) {
    adjustedY = margin;
  }

  return (
    <AnimatePresence>
      {/* Backdrop */}
      <motion.div
        className="fixed inset-0 z-40"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      />

      {/* Popup */}
      <motion.div
        className="fixed bg-slate-800/98 backdrop-blur-md rounded-xl border-2 border-cyan-400/30 shadow-2xl z-50"
        style={{
          left: adjustedX,
          top: adjustedY,
          width: popupWidth,
          height: popupHeight,
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(6, 182, 212, 0.3)'
        }}
        initial={{ scale: 0.8, opacity: 0, y: 20 }}
        animate={{ scale: 1, opacity: 1, y: 0 }}
        exit={{ scale: 0.8, opacity: 0, y: 20 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-cyan-400/20 bg-slate-700/50">
          <div className="flex items-center space-x-2">
            <BarChart3 size={16} className="text-cyan-400" />
            <h3 className="text-sm font-semibold text-cyan-100 truncate">
              {selectedElement.type.charAt(0).toUpperCase() + selectedElement.type.slice(1)} Analysis
            </h3>
          </div>
          <button
            onClick={onClose}
            className="p-1 rounded-md hover:bg-red-500/20 text-gray-300 hover:text-red-400 transition-colors border border-transparent hover:border-red-400/30"
          >
            <X size={14} />
          </button>
        </div>

        {/* Content */}
        <div className="p-3" style={{ height: 'calc(100% - 60px)' }}>
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-white text-sm">Loading...</div>
            </div>
          ) : chartData ? (
            <div style={{ width: '100%', height: '180px' }}>
              <DemandCapacityChart chartData={chartData} />
            </div>
          ) : (
            <div className="flex items-center justify-center h-32">
              <div className="text-gray-400 text-sm">No data available</div>
            </div>
          )}

          {/* Quick Stats */}
          {chartData?.metadata && (
            <div className="mt-3 grid grid-cols-3 gap-2 text-xs">
              <div className="bg-emerald-500/20 border border-emerald-400/30 rounded-lg p-2 text-center">
                <div className="text-emerald-200 text-xs">Capacity</div>
                <div className="text-emerald-400 font-bold text-sm">
                  {chartData.metadata.capacity}
                </div>
              </div>
              <div className="bg-blue-500/20 border border-blue-400/30 rounded-lg p-2 text-center">
                <div className="text-blue-200 text-xs">Demand</div>
                <div className="text-blue-400 font-bold text-sm">
                  {chartData.metadata.currentDemand}
                </div>
              </div>
              <div className={`border rounded-lg p-2 text-center ${
                (chartData.metadata.utilizationPercentage || 0) >= 90 ? 'bg-red-500/20 border-red-400/30' :
                (chartData.metadata.utilizationPercentage || 0) >= 80 ? 'bg-orange-500/20 border-orange-400/30' :
                (chartData.metadata.utilizationPercentage || 0) >= 60 ? 'bg-yellow-500/20 border-yellow-400/30' :
                'bg-emerald-500/20 border-emerald-400/30'
              }`}>
                <div className={`text-xs ${
                  (chartData.metadata.utilizationPercentage || 0) >= 90 ? 'text-red-200' :
                  (chartData.metadata.utilizationPercentage || 0) >= 80 ? 'text-orange-200' :
                  (chartData.metadata.utilizationPercentage || 0) >= 60 ? 'text-yellow-200' :
                  'text-emerald-200'
                }`}>Usage</div>
                <div className={`font-bold text-sm ${
                  (chartData.metadata.utilizationPercentage || 0) >= 90 ? 'text-red-400' :
                  (chartData.metadata.utilizationPercentage || 0) >= 80 ? 'text-orange-400' :
                  (chartData.metadata.utilizationPercentage || 0) >= 60 ? 'text-yellow-400' :
                  'text-emerald-400'
                }`}>
                  {chartData.metadata.utilizationPercentage}%
                </div>
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
