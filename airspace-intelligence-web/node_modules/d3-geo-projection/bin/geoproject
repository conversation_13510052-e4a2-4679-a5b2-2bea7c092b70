#!/usr/bin/env node

var commander = require("commander"),
    vm = require("vm"),
    d3 = Object.assign({}, require("d3-geo"), require("../")),
    read = require("./read"),
    requires = require("./requires"),
    write = require("./write");

commander
    .version(require("../package.json").version)
    .usage("[options] <projection> [file]")
    .description("Transform GeoJSON, such as to project from spherical to planar coordinates.")
    .option("-o, --out <file>", "output file name; defaults to “-” for stdout", "-")
    .option("-p, --precision <value>", "number of output digits after the decimal point")
    .option("-n, --newline-delimited", "use newline-delimited JSON")
    .option("-r, --require <[name=]module>", "require a module", requires, {d3: d3, d: undefined, i: -1})
    .parse(process.argv);

if (commander.args.length < 1) {
  console.error();
  console.error("  error: missing projection");
  console.error();
  process.exit(1);
} else if (commander.args.length > 2) {
  console.error();
  console.error("  error: multiple input files");
  console.error();
  process.exit(1);
} else if (commander.args.length === 1) {
  commander.args.push("-");
}

var sandbox = commander.require,
    context = new vm.createContext(sandbox),
    projection = new vm.Script("(" + commander.args[0] + ")"),
    reader = read(commander.args[1], commander.newlineDelimited, project).then(end).catch(abort),
    writer = write(commander.out);

function project(d, i) {
  sandbox.d = d, sandbox.i = i;
  d = d3.geoProject(d, projection.runInContext(context));
  if (commander.precision != null) d = d3.geoQuantize(d, commander.precision);
  return writer.write(JSON.stringify(d) + "\n");
}

function end() {
  return writer.end();
}

function abort(error) {
  console.error(error.stack);
}
