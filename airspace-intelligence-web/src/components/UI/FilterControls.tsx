import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
    Layers, 
    Plane, 
    MapPin, 
    AlertTriangle, 
    Shield, 
    Sword,
    Cloud,
    Clock,
    Filter,
    ChevronDown,
    ChevronUp
} from 'lucide-react';
import type { LayerVisibility, TimeRange, FilterState } from '../../types';

interface FilterControlsProps {
    layerVisibility: LayerVisibility;
    setLayerVisibility: (visibility: LayerVisibility) => void;
    timeRange: TimeRange;
    setTimeRange: (range: TimeRange) => void;
    filters: FilterState;
    setFilters: (filters: FilterState) => void;
}

export const FilterControls: React.FC<FilterControlsProps> = ({
    layerVisibility,
    setLayerVisibility,
    timeRange,
    setTimeRange,
    filters,
    setFilters
}) => {
    const [showLayerControl, setShowLayerControl] = useState(false);
    const [showTimeControl, setShowTimeControl] = useState(false);
    const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
    const [currentTime, setCurrentTime] = useState('');
    const [currentDate, setCurrentDate] = useState('');

    const timeOptions = ['now', '+1h', '+2h', '+3h', '+4h', '+5h', '+6h', '+12h', '+24h'];
    
    const layerGroups = [
        {
            title: 'Flight Data',
            icon: <Plane size={16} />,
            layers: [
                { key: 'flightLayer', label: 'Flight Paths', icon: '✈️' },
                { key: 'airportLayer', label: 'Airports', icon: '🛫' },
                { key: 'waypointLayer', label: 'Waypoints', icon: '📍' }
            ]
        },
        {
            title: 'Airspace Zones',
            icon: <Shield size={16} />,
            layers: [
                { key: 'dangerAreaLayer', label: 'Danger Areas', icon: '⚠️' },
                { key: 'restrictedAreaLayer', label: 'Restricted Areas', icon: '🚫' },
                { key: 'militaryAreaLayer', label: 'Military Areas', icon: '⚔️' },
                { key: 'sectorLayer', label: 'Sectors', icon: '🗺️' }
            ]
        },
        {
            title: 'Weather',
            icon: <Cloud size={16} />,
            layers: [
                { key: 'sigmetLayer', label: 'SIGMET', icon: '🌩️' },
                { key: 'airmetLayer', label: 'AIRMET', icon: '🌤️' }
            ]
        }
    ];

    // Update current time
    useEffect(() => {
        const timer = setInterval(() => {
            const now = new Date();
            now.setHours(now.getHours() + timeRange.offset);

            const timeString = now.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const dateString = now.toLocaleDateString('en-US', {
                weekday: 'short',
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            });
            setCurrentTime(timeString);
            setCurrentDate(dateString);
        }, 1000);

        return () => clearInterval(timer);
    }, [timeRange.offset]);

    const handleLayerToggle = (layerKey: keyof LayerVisibility) => {
        setLayerVisibility({
            ...layerVisibility,
            [layerKey]: !layerVisibility[layerKey]
        });
    };

    const handleTimeChange = (index: number) => {
        const newOffset = index;
        const now = new Date();
        now.setHours(now.getHours() + newOffset);
        
        setTimeRange({
            ...timeRange,
            offset: newOffset,
            current: now.toISOString()
        });
    };

    return (
        <div className="absolute top-4 left-4 z-20 space-y-2">
            {/* Main Control Panel */}
            <motion.div
                className="bg-slate-800/95 backdrop-blur-md rounded-xl border-2 border-cyan-400/30 p-4 text-white shadow-2xl"
                style={{
                    boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(6, 182, 212, 0.2)'
                }}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
            >
                <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-bold text-cyan-100">Airspace Intelligence</h3>
                    <div className="flex space-x-2">
                        <button
                            onClick={() => setShowTimeControl(!showTimeControl)}
                            className="p-2 rounded-lg bg-blue-500/30 hover:bg-blue-500/50 border border-blue-400/30 hover:border-blue-400/60 transition-all duration-200"
                            title="Time Controls"
                        >
                            <Clock size={16} className="text-blue-300" />
                        </button>
                        <button
                            onClick={() => setShowLayerControl(!showLayerControl)}
                            className="p-2 rounded-lg bg-emerald-500/30 hover:bg-emerald-500/50 border border-emerald-400/30 hover:border-emerald-400/60 transition-all duration-200"
                            title="Layer Controls"
                        >
                            <Layers size={16} className="text-emerald-300" />
                        </button>
                        <button
                            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                            className="p-2 rounded-lg bg-purple-500/30 hover:bg-purple-500/50 border border-purple-400/30 hover:border-purple-400/60 transition-all duration-200"
                            title="Advanced Filters"
                        >
                            <Filter size={16} className="text-purple-300" />
                        </button>
                    </div>
                </div>

                {/* Current Time Display */}
                <div className="bg-slate-700/50 border border-cyan-400/20 rounded-lg p-3 text-center">
                    <div className="text-xs text-cyan-200">{currentDate}</div>
                    <div className="text-xl font-bold text-cyan-100">{currentTime}</div>
                    <div className="text-xs text-cyan-300">UTC+7 (+{timeRange.offset}h)</div>
                </div>
            </motion.div>

            {/* Time Control Panel */}
            <AnimatePresence>
                {showTimeControl && (
                    <motion.div
                        className="bg-slate-800/95 backdrop-blur-md rounded-xl border-2 border-blue-400/30 p-4 text-white shadow-xl"
                        initial={{ opacity: 0, height: 0, y: -10 }}
                        animate={{ opacity: 1, height: 'auto', y: 0 }}
                        exit={{ opacity: 0, height: 0, y: -10 }}
                        transition={{ duration: 0.3, ease: "easeOut" }}
                    >
                        <div className="flex items-center justify-between mb-3">
                            <h4 className="font-semibold flex items-center">
                                <Clock size={16} className="mr-2" />
                                Time Control
                            </h4>
                            <button
                                onClick={() => setShowTimeControl(false)}
                                className="text-gray-400 hover:text-white"
                            >
                                <ChevronUp size={16} />
                            </button>
                        </div>
                        
                        <div className="grid grid-cols-3 gap-2">
                            {timeOptions.map((option, index) => (
                                <button
                                    key={option}
                                    onClick={() => handleTimeChange(index)}
                                    className={`p-2 rounded-md text-sm transition-colors ${
                                        timeRange.offset === index
                                            ? 'bg-blue-600 text-white'
                                            : 'bg-white/10 hover:bg-white/20'
                                    }`}
                                >
                                    {option}
                                </button>
                            ))}
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Layer Control Panel */}
            <AnimatePresence>
                {showLayerControl && (
                    <motion.div
                        className="bg-slate-800/95 backdrop-blur-md rounded-xl border-2 border-emerald-400/30 p-4 text-white max-w-sm shadow-xl"
                        initial={{ opacity: 0, height: 0, y: -10 }}
                        animate={{ opacity: 1, height: 'auto', y: 0 }}
                        exit={{ opacity: 0, height: 0, y: -10 }}
                        transition={{ duration: 0.3, ease: "easeOut" }}
                    >
                        <div className="flex items-center justify-between mb-3">
                            <h4 className="font-semibold flex items-center">
                                <Layers size={16} className="mr-2" />
                                Layer Control
                            </h4>
                            <button
                                onClick={() => setShowLayerControl(false)}
                                className="text-gray-400 hover:text-white"
                            >
                                <ChevronUp size={16} />
                            </button>
                        </div>

                        <div className="space-y-4">
                            {layerGroups.map((group) => (
                                <div key={group.title}>
                                    <div className="flex items-center mb-2 text-sm font-medium text-gray-300">
                                        {group.icon}
                                        <span className="ml-2">{group.title}</span>
                                    </div>
                                    <div className="space-y-1 ml-4">
                                        {group.layers.map((layer) => (
                                            <label
                                                key={layer.key}
                                                className="flex items-center text-sm cursor-pointer hover:bg-white/10 rounded p-1"
                                            >
                                                <input
                                                    type="checkbox"
                                                    checked={layerVisibility[layer.key as keyof LayerVisibility]}
                                                    onChange={() => handleLayerToggle(layer.key as keyof LayerVisibility)}
                                                    className="mr-2 rounded"
                                                />
                                                <span className="mr-2">{layer.icon}</span>
                                                <span>{layer.label}</span>
                                            </label>
                                        ))}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    );
};
