import { useState } from 'react';
import { LeafletMap } from './components/Map/LeafletMap';
import { TimeRadio } from './components/UI/widgets';
import type { LayerVisibility } from './types/weather.types';
import './App.css';
import { StatusCard } from './components/UI/status';
import type { GeoJsonObject } from 'geojson';

function App() {
  const [showLayerControl, setShowLayerControl] = useState(false);
  const [mapView, setMapview] = useState<L.LatLng | null>(null)
  const [mapGeo, setMapGeo] = useState<GeoJsonObject | null>(null)
  const [layerVisibility, setLayerVisibility] = useState<LayerVisibility>({
    sigmetLayer: true,
    airmetLayer: true
  });

  return (
    <div className="w-full relative">
      <LeafletMap
        layerVisibility={layerVisibility}
        mapView={mapView}
        mapGeo={mapGeo}
      />
      <TimeRadio
        showLayerControl={showLayerControl}
        setShowLayerControl={setShowLayerControl}
        layerVisibility={layerVisibility}
        setLayerVisibility={setLayerVisibility}

      />
      <StatusCard
        layerVisibility={layerVisibility}
        setMapView={setMapview}
        setMapGeo={setMapGeo}
      />
    </div>

  );
}

export default App;