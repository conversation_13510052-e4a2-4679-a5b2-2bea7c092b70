import React, { useEffect, useRef } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { MAP_CONFIG, LAYER_STYLES } from '../../utils/constants';
import type { LayerVisibility } from '../../types/weather.types';
import { WeatherLabelManager } from '../Weather/WeatherLabels';
import { WeatherPopupGenerator } from '../Weather/WeatherPopup';
import { useWeatherData } from '../../hooks/useWeatherData';
import type { GeoJsonObject } from 'geojson';
// import type { GeoJsonObject, LineString, Feature } from 'geojson';

// Configure default marker icon
import icon from 'leaflet/dist/images/marker-icon.png';
import iconShadow from 'leaflet/dist/images/marker-shadow.png';


const DefaultIcon = L.icon({
    iconUrl: icon,
    shadowUrl: iconShadow,
    iconSize: [25, 41],
    iconAnchor: [12, 41]
});
L.Marker.prototype.options.icon = DefaultIcon;

interface LeafletMapProps {
    layerVisibility: LayerVisibility;
    mapView: L.LatLng | null
    mapGeo: GeoJsonObject | null
}

export const LeafletMap: React.FC<LeafletMapProps> = ({ layerVisibility, mapView, mapGeo }) => {
    const mapRef = useRef<L.Map | null>(null);
    const hoverLaverRef = useRef<L.GeoJSON | null>(null)

    const layersRef = useRef<{
        sigmetLayer: L.GeoJSON | null;
        airmetLayer: L.GeoJSON | null;
    }>({ sigmetLayer: null, airmetLayer: null });

    const labelManagersRef = useRef<{
        sigmet: WeatherLabelManager | null;
        airmet: WeatherLabelManager | null;
    }>({ sigmet: null, airmet: null });

    const { sigmetFeatures, airmetFeatures, error, loading } = useWeatherData();

    ////////////////////////////// test /////////////////////////////////////
    // var geojsonFeature: Feature<LineString> = {
    //     "type": "Feature",
    //     "properties": {
    //         "stroke": "#aeff00",
    //         "stroke-width": 2,
    //         "stroke-opacity": 1
    //     },
    //     "geometry": {
    //         "coordinates": [
    //             [
    //                 72.2809381360031,
    //                 -52.71045814083356
    //             ],
    //             [
    //                 72.39726454097851,
    //                 -48.71141230773455
    //             ],
    //             [
    //                 75.15032279208114,
    //                 -47.85997895491364
    //             ],
    //             [
    //                 84.61153706347488,
    //                 -43.83820780481379
    //             ],
    //             [
    //                 93.45274990112046,
    //                 -48.06131777772665
    //             ],
    //             [
    //                 106.1820655921374,
    //                 -48.2648595146913
    //             ],
    //             [
    //                 111.5876654061314,
    //                 -47.298227044117105
    //             ],
    //             [
    //                 118.2673680565286,
    //                 -48.61707562636288
    //             ],
    //             [
    //                 119.8799611703572,
    //                 -43.178391955227006
    //             ],
    //             [
    //                 94.91463074145258,
    //                 -38.71514274131855
    //             ],
    //             [
    //                 106.80003924707955,
    //                 -32.80777449533548
    //             ],
    //             [
    //                 118.6854477527063,
    //                 -38.808285677510135
    //             ],
    //             [
    //                 132.18344937216176,
    //                 -51.23091543059344
    //             ],
    //             [
    //                 140.06724016156892,
    //                 -47.66055573733402
    //             ],
    //             [
    //                 146.09816549670728,
    //                 -45.43485750338134
    //             ],
    //             [
    //                 146.99405055994612,
    //                 -42.51299291423599
    //             ],
    //             [
    //                 147.53158159788939,
    //                 -38.752628086456745
    //             ],
    //             [
    //                 151.8915554042976,
    //                 -36.482122313698255
    //             ],
    //             [
    //                 150.91938124159253,
    //                 -35.3779593743876
    //             ],
    //             [
    //                 150.62075288718034,
    //                 -29.84790379781225
    //             ],
    //             [
    //                 146.26063863428305,
    //                 -25.512536619165573
    //             ],
    //             [
    //                 125.65528217980409,
    //                 -26.264774657704756
    //             ],
    //             [
    //                 126.0136362050996,
    //                 -18.825025770804018
    //             ],
    //             [
    //                 113.23234263623505,
    //                 -15.285552638010103
    //             ]
    //         ],
    //         "type": "LineString"
    //     }
    // }

    /////////////////////////////////////////////////////////////////////////

    // Initialize map
    useEffect(() => {
        const mapContainer = document.getElementById('map');
        if (!mapContainer || mapRef.current) return;

        const map = L.map('map', {
            center: MAP_CONFIG.center,
            zoom: MAP_CONFIG.zoom,
            zoomControl: false
        });

        // Add base layer
        L.tileLayer(MAP_CONFIG.styles.dark.url, {
            attribution: MAP_CONFIG.styles.dark.attribution
        }).addTo(map);

        // L.geoJSON(geojsonFeature).addTo(map);

        // Create panes for layer organization
        map.createPane('sigmetPane');
        map.createPane('airmetPane');
        map.createPane('hoverPane');

        const sigmetPane = map.getPane('sigmetPane');
        const airmetPane = map.getPane('airmetPane');
        const hoverPane = map.getPane('hoverPane');

        if (sigmetPane) sigmetPane.style.zIndex = '650';
        if (airmetPane) airmetPane.style.zIndex = '650';
        if (hoverPane) hoverPane.style.zIndex = '650';

        // Create layers
        const sigmetLayer = L.geoJSON(null, {
            style: LAYER_STYLES.sigmet,
            onEachFeature: WeatherPopupGenerator.attachPopup,
            pane: 'sigmetPane'
        }).addTo(map);

        const airmetLayer = L.geoJSON(null, {
            style: LAYER_STYLES.airmet,
            onEachFeature: WeatherPopupGenerator.attachPopup,
            pane: 'airmetPane'
        }).addTo(map);

        // Initialize label managers
        const sigmetLabelManager = new WeatherLabelManager(map);
        const airmetLabelManager = new WeatherLabelManager(map);

        // Store references
        mapRef.current = map;
        layersRef.current = { sigmetLayer, airmetLayer };
        labelManagersRef.current = {
            sigmet: sigmetLabelManager,
            airmet: airmetLabelManager
        };

        return () => {
            if (mapRef.current) {
                labelManagersRef.current.sigmet?.destroy();
                labelManagersRef.current.airmet?.destroy();
                mapRef.current.remove();
                mapRef.current = null;
            }
        };
    }, []);

    // get zoom to hover
    useEffect(() => {
        if (mapView && mapRef.current) {
            mapRef.current.setView(mapView, mapRef.current.getZoom());
        }

    }, [mapView])

    useEffect(() => {
        if (mapGeo && mapRef.current) {
            if (hoverLaverRef.current) {
                mapRef.current.removeLayer(hoverLaverRef.current)
                hoverLaverRef.current = null
            }
            const hoverLayer = L.geoJSON(mapGeo, {
                style: LAYER_STYLES.hover,
                onEachFeature: WeatherPopupGenerator.attachPopup,
                pane: 'hoverPane'
            }).addTo(mapRef.current);

            hoverLaverRef.current = hoverLayer

            const fadeTimeout = setTimeout(() => {
                // Remove after fade duration
                setTimeout(() => {
                    if (hoverLaverRef.current) {
                        mapRef.current?.removeLayer(hoverLaverRef.current)
                        hoverLaverRef.current = null
                    }
                }, 500)
            }, 5000)

            return () => {
                clearTimeout(fadeTimeout)
                if (hoverLaverRef.current) {
                    mapRef.current?.removeLayer(hoverLaverRef.current)
                    hoverLaverRef.current = null
                }
            }
        }
    }, [mapGeo, mapRef])


    // Update layers with new data
    useEffect(() => {
        if (!layersRef.current.sigmetLayer || !layersRef.current.airmetLayer) return;

        // Clear existing data
        layersRef.current.sigmetLayer.clearLayers();
        layersRef.current.airmetLayer.clearLayers();

        // Add new data
        sigmetFeatures.forEach(feature => {
            layersRef.current.sigmetLayer?.addData(feature);
        });

        airmetFeatures.forEach(feature => {
            layersRef.current.airmetLayer?.addData(feature);
        });

        // Update labels based on visibility
        if (layerVisibility.sigmetLayer && labelManagersRef.current.sigmet) {
            labelManagersRef.current.sigmet.addLabels(layersRef.current.sigmetLayer, 'sigmet');
        }

        if (layerVisibility.airmetLayer && labelManagersRef.current.airmet) {
            labelManagersRef.current.airmet.addLabels(layersRef.current.airmetLayer, 'airmet');
        }
    }, [sigmetFeatures, airmetFeatures, layerVisibility]);

    // Handle layer visibility changes
    useEffect(() => {
        if (!mapRef.current || !layersRef.current.sigmetLayer || !layersRef.current.airmetLayer) return;

        // Toggle SIGMET layer
        if (layerVisibility.sigmetLayer) {
            layersRef.current.sigmetLayer.addTo(mapRef.current);
        } else {
            layersRef.current.sigmetLayer.remove();
            labelManagersRef.current.sigmet?.clearLabels();
        }

        // Toggle AIRMET layer
        if (layerVisibility.airmetLayer) {
            layersRef.current.airmetLayer.addTo(mapRef.current);
        } else {
            layersRef.current.airmetLayer.remove();
            labelManagersRef.current.airmet?.clearLabels();
        }
    }, [layerVisibility]);

    if (error) {
        return (
            <div className="flex items-center justify-center h-full bg-gray-800 text-white">
                <div className="text-center">
                    <h3 className="text-lg font-semibold mb-2">Error Loading Weather Data</h3>
                    <p className="text-gray-300">{error}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="relative h-full w-full z-0">
            <div id="map" className="h-full w-full" style={{ backgroundColor: '#262626' }} />
            {loading && (
                <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded">
                    Loading weather data...
                </div>

            )}
        </div>
    );
};