&, div {
    direction: ltr;
    font-family: 'Open Sans', verdana, arial, sans-serif;
    margin: 0;
    padding: 0;
}

input, button {
    font-family: 'Open Sans', verdana, arial, sans-serif;

    &:focus {
        outline: none;
    }
}

a {
    text-decoration: none;

    &:hover {
        text-decoration: none;
    }
}

.crisp { shape-rendering: crispEdges; }

.user-select-none {
    @include vendor('user-select', none);
}

svg a { fill: $color-brand-primary; }
svg a:hover { fill: #3c6dc5; }

.main-svg {
    position: absolute;
    top: 0;
    left: 0;
    pointer-events: none;

    .draglayer {
        pointer-events: all;
    }
}
