export class TimeCalculator {
    static calculateTimeRemaining(validEnd: string): string {
        try {
            const endTime = new Date(validEnd)
            const now = new Date()
            const timeDiff = endTime.getTime() - now.getTime()

            if (timeDiff <= 0) return 'Expired';

            const hours = Math.floor(timeDiff / (1000 * 60 * 60));
            const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))

            if (hours > 24) {
                const days = Math.floor(hours / 24)
                const remaingingHours = hours % 24
                return `${days}d ${remaingingHours}h ${minutes}m`
            } else if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else {
                return `${minutes}m`;
            }
        } catch (error) {
            console.error('Error calculating time remaining:', error)
            return 'Invalid date'

        }
    }
}