import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, BarChart3, TrendingUp } from 'lucide-react';
import { DemandCapacityChart } from './DemandCapacityChart';
import { FlightDataService } from '../../services/FlightDataService';
import type { ChartData, SelectedElement } from '../../types';

interface ChartPopupProps {
  isOpen: boolean;
  onClose: () => void;
  selectedElement: SelectedElement;
  position: { x: number; y: number };
}

export const ChartPopup: React.FC<ChartPopupProps> = ({
  isOpen,
  onClose,
  selectedElement,
  position
}) => {
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState(false);

  // Generate chart data when popup opens
  useEffect(() => {
    if (!isOpen) return;

    const generateChartData = async () => {
      setLoading(true);
      try {
        const flightService = FlightDataService.getInstance();
        const newData = flightService.generateDemandCapacityData(
          selectedElement.id,
          selectedElement.type as 'airport' | 'sector' | 'waypoint'
        );

        const newChartData: ChartData = {
          type: 'demand-capacity',
          title: `${selectedElement.type.charAt(0).toUpperCase() + selectedElement.type.slice(1)} - ${selectedElement.id}`,
          timeRange: {
            start: new Date().toISOString(),
            end: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          },
          data: newData,
          metadata: selectedElement.data?.capacity ? {
            capacity: selectedElement.data.capacity.totalHourly || selectedElement.data.controllerCapacity || 50,
            currentDemand: selectedElement.data.capacity?.currentDemand || selectedElement.data.currentTraffic || 30,
            utilizationPercentage: selectedElement.data.capacity?.utilizationPercentage || selectedElement.data.utilizationPercentage || 60
          } : {
            capacity: 50,
            currentDemand: 30,
            utilizationPercentage: 60
          }
        };

        setChartData(newChartData);
      } catch (error) {
        console.error('Error generating chart data:', error);
      } finally {
        setLoading(false);
      }
    };

    generateChartData();
  }, [isOpen, selectedElement]);

  if (!isOpen) return null;

  // Calculate popup position to keep it on screen
  const popupWidth = 400;
  const popupHeight = 300;
  const margin = 20;
  
  let adjustedX = position.x;
  let adjustedY = position.y;
  
  // Adjust horizontal position
  if (adjustedX + popupWidth > window.innerWidth - margin) {
    adjustedX = window.innerWidth - popupWidth - margin;
  }
  if (adjustedX < margin) {
    adjustedX = margin;
  }
  
  // Adjust vertical position
  if (adjustedY + popupHeight > window.innerHeight - margin) {
    adjustedY = window.innerHeight - popupHeight - margin;
  }
  if (adjustedY < margin) {
    adjustedY = margin;
  }

  return (
    <AnimatePresence>
      <motion.div
        className="fixed bg-gray-900/95 backdrop-blur-sm rounded-lg border border-white/20 shadow-2xl z-50"
        style={{
          left: adjustedX,
          top: adjustedY,
          width: popupWidth,
          height: popupHeight
        }}
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-white/20">
          <div className="flex items-center space-x-2">
            <BarChart3 size={16} className="text-blue-400" />
            <h3 className="text-sm font-semibold text-white truncate">
              {selectedElement.type.charAt(0).toUpperCase() + selectedElement.type.slice(1)} Analysis
            </h3>
          </div>
          <button
            onClick={onClose}
            className="p-1 rounded hover:bg-white/10 text-gray-400 hover:text-white transition-colors"
          >
            <X size={14} />
          </button>
        </div>

        {/* Content */}
        <div className="p-3 h-full">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="text-white text-sm">Loading...</div>
            </div>
          ) : chartData ? (
            <div className="h-48">
              <DemandCapacityChart chartData={chartData} />
            </div>
          ) : (
            <div className="flex items-center justify-center h-32">
              <div className="text-gray-400 text-sm">No data available</div>
            </div>
          )}

          {/* Quick Stats */}
          {chartData?.metadata && (
            <div className="mt-2 grid grid-cols-3 gap-2 text-xs">
              <div className="bg-white/10 rounded p-1 text-center">
                <div className="text-gray-300">Capacity</div>
                <div className="text-green-400 font-bold">
                  {chartData.metadata.capacity}
                </div>
              </div>
              <div className="bg-white/10 rounded p-1 text-center">
                <div className="text-gray-300">Demand</div>
                <div className="text-blue-400 font-bold">
                  {chartData.metadata.currentDemand}
                </div>
              </div>
              <div className="bg-white/10 rounded p-1 text-center">
                <div className="text-gray-300">Usage</div>
                <div className={`font-bold ${
                  (chartData.metadata.utilizationPercentage || 0) >= 90 ? 'text-red-400' :
                  (chartData.metadata.utilizationPercentage || 0) >= 80 ? 'text-orange-400' :
                  (chartData.metadata.utilizationPercentage || 0) >= 60 ? 'text-blue-400' :
                  'text-green-400'
                }`}>
                  {chartData.metadata.utilizationPercentage}%
                </div>
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
