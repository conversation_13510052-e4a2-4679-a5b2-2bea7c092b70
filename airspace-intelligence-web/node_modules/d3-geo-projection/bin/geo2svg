#!/usr/bin/env node

var os = require("os"),
    commander = require("commander"),
    d3 = Object.assign({}, require("d3-geo"), require("../")),
    read = require("./read"),
    write = require("./write");

commander
    .version(require("../package.json").version)
    .usage("[options] [file]")
    .description("Convert GeoJSON to SVG.")
    .option("-o, --out <file>", "output file name; defaults to “-” for stdout", "-")
    .option("-w, --width <value>", "output width", 960)
    .option("-h, --height <value>", "output height", 500)
    .option("-p, --precision <value>", "number of output digits after the decimal point", 6)
    .option("-n, --newline-delimited", "accept newline-delimited JSON")
    .option("--fill <value>", "default fill", "none")
    .option("--stroke <value>", "default stroke", "black")
    .option("-r, --radius <value>", "default point radius", 4.5)
    .parse(process.argv);

if (commander.args.length === 0) commander.args[0] = "-";
else if (commander.args.length !== 1) {
  console.error();
  console.error("  error: multiple input files");
  console.error();
  process.exit(1);
}

var reader = read(commander.args[0], commander.newlineDelimited, transform).then(end).catch(abort),
    writer = write(commander.out),
    path = d3.geoPath().pointRadius(function(d) { var p = d.properties, v; return p && (v = p["point-radius"] || p.pointRadius) != null ? v : commander.radius; }),
    render = commander.precision == null ? path : function(d) { return path(d3.geoQuantize(d, commander.precision)); };

writer.write("<?xml version=\"1.0\" encoding=\"utf-8\"?>" + os.EOL
    + "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">" + os.EOL
    + "<!-- Generated by geo2svg " + require("../package.json").version + ". https://d3js.org/d3-geo-projection/ -->" + os.EOL
    + "<svg version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\""
    + " width=\"" + +commander.width + "\""
    + " height=\"" + +commander.height + "\""
    + " viewBox=\"0 0 " + +commander.width + " " + +commander.height + "\""
    + (commander.fill != "black" ? " fill=\"" + attr(commander.fill) + "\"" : "")
    + (commander.stroke != "none" ? " stroke=\"" + attr(commander.stroke) + "\"" : "")
    + ">" + os.EOL);

function transform(d) {
  var p = d.properties, v;
  return writer.write("  <path"
      + ((v = d.id) != null ? " id=\"" + attr(v + "") + "\"" : "")
      + (p ? ((v = p["fill"]) != null ? " fill=\"" + attr(v + "") + "\"" : "")
        + ((v = p["fill-rule"] || p.fillRule) != null ? " fill-rule=\"" + attr(v + "") + "\"" : "")
        + ((v = p["fill-opacity"] || p.fillOpacity) != null ? " fill-opacity=\"" + attr(v + "") + "\"" : "")
        + ((v = p["stroke"]) != null ? " stroke=\"" + attr(v + "") + "\"" : "")
        + ((v = p["stroke-width"] || p.strokeWidth) != null ? " stroke-width=\"" + attr(v + "") + "\"" : "")
        + ((v = p["stroke-linecap"] || p.strokeLinecap) != null ? " stroke-linecap=\"" + attr(v + "") + "\"" : "")
        + ((v = p["stroke-linejoin"] || p.strokeLinejoin) != null ? " stroke-linejoin=\"" + attr(v + "") + "\"" : "")
        + ((v = p["stroke-miterlimit"] || p.strokeMiterlimit) != null ? " stroke-miterlimit=\"" + attr(v + "") + "\"" : "")
        + ((v = p["stroke-dasharray"] || p.strokeDasharray) != null ? " stroke-dasharray=\"" + attr(v + "") + "\"" : "")
        + ((v = p["stroke-dashoffset"] || p.strokeDashoffset) != null ? " stroke-dashoffset=\"" + attr(v + "") + "\"" : "")
        + ((v = p["stroke-opacity"] || p.strokeOpacity) != null ? " stroke-opacity=\"" + attr(v + "") + "\"" : "")
      : "")
      + (v = render(d), v ? " d=\"" + v + "\"" : "")
      + ">"
      + ((v = p && p["title"]) != null ? "<title>" + text(v + "") + "</title>" : "")
      + "</path>" + os.EOL);
}

function end() {
  return writer.write("</svg>" + os.EOL);
}

function abort(error) {
  console.error(error.stack);
}

function text(string) {
  return string.replace(/[&<>]/g, function(character) {
    switch (character) {
      case "&": return "&amp;";
      case "<": return "&lt;";
      case ">": return "&gt;";
    }
  });
}

function attr(string) {
  return string.replace(/"/g, "&quot;");
}
