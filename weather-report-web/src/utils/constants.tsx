export const MAP_CONFIG = {
    center: [13.751648569129001, 100.50065116808389] as [number, number],
    zoom: 3.5,
    refreshInterval: 10 * 1000, // 10 seconds
    styles: {
        dark: {
            url: 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png',
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>'
        }
    }
};

export const LAYER_STYLES = {
    sigmet: { color: 'red', weight: 2.5, fillOpacity: 0.1 },
    airmet: { color: 'green', weight: 2.5, fillOpacity: 0.1 },
    hover: { color: 'orange', weight: 2.5, fillOpacity: 0.1 }
}

export const API_ENDPOINTS = {
    wfs: 'http://localhost/geoserver/weatherws/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=weatherws:fir_positions&outputFormat=application/json'
}

export const FIR_NAME = {
    "HONIARA FIR": "SB",
    "NAURU FIR": "NR",
    "PORT MORESBY FIR": "PG",
    "COLOMBO FIR": "LK",
    "HONG KONG FIR": "HK",
    "MACAO FIR": "MO",
    "HO CHI MINH FIR": "VN",
    "HANOI FIR": "VN",
    "JAKARTA FIR": "ID",
    "KOTA KINABALU FIR": "MY",
    "KUALA LUMPUR FIR": "MY",
    "SINGAPORE FIR": "SG",
    "BRISBANE FIR": "AU",
    "MELBOURNE FIR": "AU",
    "AUCKLAND FIR": "NZ",
    "HONOLULU FIR": "US",
    "GUAM FIR": "US",
    "TOKYO FIR": "JP",
    "INCHEON FIR": "KR",
    "MANILA FIR": "PH",
    "BANGKOK FIR": "TH",
    "YANGON FIR": "MM",
    "BEIJING FIR": "CN",
    "GUANGZHOU FIR": "CN",
    "WUHAN FIR": "CN",
    "SANYA FIR": "CN",
    "LANZHOU FIR": "CN",
    "DALIAN FIR": "CN",
    "KUNMING FIR": "CN",
    "SHANGHAI FIR": "CN",
    "CHENGDU FIR": "CN",
    "HOHHOT FIR": "CN"
}