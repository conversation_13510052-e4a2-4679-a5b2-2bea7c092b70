* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #root {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.leaflet-container {
  width: 100vw;
  height: 100vh;
}

#root {
  margin: 0 !important;
  padding: 0 !important;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

body {
  margin: 0;
  padding: 0;
}
.leaflet-popup.my-custom-popup .leaflet-popup-content-wrapper {
  background: #f9f9f9;
  border-radius: 6px;
  padding: 8px;
}

/* Hide up/down arrow buttons in WebKit browsers */
.custom-scrollbar-left::-webkit-scrollbar-button {
  display: none;
}

/* Optional: style the scrollbar itself */
.custom-scrollbar-left::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}

.custom-scrollbar-left::-webkit-scrollbar-thumb {
  background: #8884;
  border-radius: 4px;
}

@import "tailwindcss";
