import { useState } from 'react';
import { AirspaceMap } from './components/Map/AirspaceMap';
import { FilterControls } from './components/UI/FilterControls';
import { ChartPopup } from './components/Charts/ChartPopup';
import type { LayerVisibility, MapClickEvent, SelectedElement, TimeRange, FilterState } from './types';
import './App.css';

function App() {
  const [mapView, setMapView] = useState<L.LatLng | null>(null);
  const [mapGeo, setMapGeo] = useState<GeoJSON.GeoJsonObject | null>(null);
  const [selectedElement, setSelectedElement] = useState<SelectedElement | null>(null);

  const [layerVisibility, setLayerVisibility] = useState<LayerVisibility>({
    sigmetLayer: false, // Start with weather hidden
    airmetLayer: false,
    flightLayer: true,
    dangerAreaLayer: true,
    restrictedAreaLayer: true,
    militaryAreaLayer: true,
    airportLayer: true,
    sectorLayer: false, // Start with sectors hidden for cleaner view
    waypointLayer: true
  });

  const [timeRange, setTimeRange] = useState<TimeRange>({
    start: new Date().toISOString(),
    end: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    current: new Date().toISOString(),
    offset: 0
  });

  const [filters, setFilters] = useState<FilterState>({
    flight: {},
    airspace: { activeOnly: true },
    weather: { types: ['sigmet', 'airmet'], severityLevels: [] }
  });

  const [showChartPopup, setShowChartPopup] = useState(false);
  const [chartPopupPosition, setChartPopupPosition] = useState({ x: 0, y: 0 });

  const handleMapClick = (event: MapClickEvent) => {
    console.log('Map clicked:', event);

    if (event.elementType && event.elementId) {
      const element = {
        type: event.elementType,
        id: event.elementId,
        data: event.elementData,
        coordinates: event.coordinates
      };

      setSelectedElement(element);

      // Show chart popup for clickable elements
      if (['airport', 'sector', 'waypoint'].includes(event.elementType)) {
        // Convert lat/lng to screen coordinates for popup positioning
        const mapElement = document.getElementById('map');
        if (mapElement) {
          const rect = mapElement.getBoundingClientRect();
          // For now, position popup near the center-right of screen
          // In a real implementation, you'd convert the lat/lng to pixel coordinates
          setChartPopupPosition({
            x: Math.min(window.innerWidth - 420, rect.width * 0.6),
            y: Math.max(100, rect.height * 0.3)
          });
          setShowChartPopup(true);
        }
      }
    } else {
      setSelectedElement(null);
      setShowChartPopup(false);
    }
  };

  return (
    <div className="w-screen h-screen relative bg-gray-900 overflow-hidden">
      <AirspaceMap
        layerVisibility={layerVisibility}
        mapView={mapView}
        mapGeo={mapGeo}
        onMapClick={handleMapClick}
        selectedElement={selectedElement}
      />

      <FilterControls
        layerVisibility={layerVisibility}
        setLayerVisibility={setLayerVisibility}
        timeRange={timeRange}
        setTimeRange={setTimeRange}
        filters={filters}
        setFilters={setFilters}
      />

      {/* Selected Element Info */}
      {selectedElement && !showChartPopup && (
        <div className="absolute top-4 right-4 bg-black/70 backdrop-blur-sm text-white p-3 rounded-lg z-10 max-w-xs">
          <h4 className="text-sm font-bold mb-2">Selected</h4>
          <div className="space-y-1 text-xs">
            <p><strong>{selectedElement.type}:</strong> {selectedElement.id}</p>
            <p className="text-gray-300">
              {selectedElement.coordinates[0].toFixed(4)}, {selectedElement.coordinates[1].toFixed(4)}
            </p>
            {['airport', 'sector', 'waypoint'].includes(selectedElement.type) && (
              <p className="text-blue-300 mt-1">Click for analysis chart</p>
            )}
          </div>
        </div>
      )}

      {/* Chart Popup */}
      {showChartPopup && selectedElement && (
        <ChartPopup
          isOpen={showChartPopup}
          onClose={() => setShowChartPopup(false)}
          selectedElement={selectedElement}
          position={chartPopupPosition}
        />
      )}
    </div>
  );
}

export default App;
