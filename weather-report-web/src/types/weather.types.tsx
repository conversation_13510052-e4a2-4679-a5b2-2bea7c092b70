export interface WeatherProperties {
    cancelled: boolean;
    create_at: string;
    item_id: string;
    locations: string;
    types: string;
    valid_end: string;
    hazard: string;
    valid_start: string;
    lower: string;
    upper: string;
}

export interface WeatherFeature extends GeoJSON.Feature {
    properties: WeatherProperties;
}

export interface LayerVisibility {
    sigmetLayer: boolean;
    airmetLayer: boolean;
}

export type WeatherType = 'sigmet' | 'airmet';