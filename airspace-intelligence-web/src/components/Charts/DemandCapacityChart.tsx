import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  LineElement,
  PointElement,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import type { ChartData, ChartDataPoint } from '../../types';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend
);

interface DemandCapacityChartProps {
  chartData: ChartData;
  title?: string;
}

export const DemandCapacityChart: React.FC<DemandCapacityChartProps> = ({ 
  chartData, 
  title 
}) => {
  const data = {
    labels: chartData.data.map(point => point.time),
    datasets: [
      {
        label: 'Capacity',
        data: chartData.data.map(point => point.capacity),
        backgroundColor: 'rgba(34, 197, 94, 0.5)',
        borderColor: 'rgba(34, 197, 94, 1)',
        borderWidth: 2,
        type: 'line' as const,
        tension: 0.1,
        pointRadius: 4,
        pointHoverRadius: 6,
      },
      {
        label: 'Current Demand',
        data: chartData.data.map(point => point.demand),
        backgroundColor: chartData.data.map(point => {
          const utilization = point.utilization;
          if (utilization >= 90) return 'rgba(239, 68, 68, 0.8)'; // Red
          if (utilization >= 80) return 'rgba(245, 158, 11, 0.8)'; // Orange
          if (utilization >= 60) return 'rgba(59, 130, 246, 0.8)'; // Blue
          return 'rgba(34, 197, 94, 0.8)'; // Green
        }),
        borderColor: chartData.data.map(point => {
          const utilization = point.utilization;
          if (utilization >= 90) return 'rgba(239, 68, 68, 1)';
          if (utilization >= 80) return 'rgba(245, 158, 11, 1)';
          if (utilization >= 60) return 'rgba(59, 130, 246, 1)';
          return 'rgba(34, 197, 94, 1)';
        }),
        borderWidth: 1,
      }
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: 'white',
          font: {
            size: 10
          }
        }
      },
      title: {
        display: false, // Hide title in compact view
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
        callbacks: {
          afterBody: function(context: any) {
            const dataIndex = context[0].dataIndex;
            const point = chartData.data[dataIndex];
            return [
              `Utilization: ${point.utilization}%`,
              `Status: ${point.utilization >= 90 ? 'Critical' : 
                        point.utilization >= 80 ? 'High' : 
                        point.utilization >= 60 ? 'Moderate' : 'Normal'}`
            ];
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
        ticks: {
          color: 'white',
          font: {
            size: 9
          }
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
        ticks: {
          color: 'white',
          font: {
            size: 9
          }
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'index' as const,
    },
  };

  return (
    <div className="w-full h-full">
      <Bar data={data} options={options} />
      
      {/* Summary Statistics */}
      {chartData.metadata && (
        <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
          <div className="bg-white/10 rounded p-2 text-center">
            <div className="text-xs text-gray-300">Current Capacity</div>
            <div className="text-lg font-bold text-green-400">
              {chartData.metadata.capacity}
            </div>
          </div>
          <div className="bg-white/10 rounded p-2 text-center">
            <div className="text-xs text-gray-300">Current Demand</div>
            <div className="text-lg font-bold text-blue-400">
              {chartData.metadata.currentDemand}
            </div>
          </div>
          <div className="bg-white/10 rounded p-2 text-center">
            <div className="text-xs text-gray-300">Utilization</div>
            <div className={`text-lg font-bold ${
              (chartData.metadata.utilizationPercentage || 0) >= 90 ? 'text-red-400' :
              (chartData.metadata.utilizationPercentage || 0) >= 80 ? 'text-orange-400' :
              (chartData.metadata.utilizationPercentage || 0) >= 60 ? 'text-blue-400' :
              'text-green-400'
            }`}>
              {chartData.metadata.utilizationPercentage}%
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
