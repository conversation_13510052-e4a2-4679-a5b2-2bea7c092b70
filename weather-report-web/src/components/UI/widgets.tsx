import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import { Layers } from 'lucide-react';
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import type { Variants } from 'framer-motion';



interface TimeRadioProps {
    showLayerControl: boolean;
    setShowLayerControl: (show: boolean) => void;
    layerVisibility: {
        sigmetLayer: boolean;
        airmetLayer: boolean;
    };
    setLayerVisibility: React.Dispatch<React.SetStateAction<{
        sigmetLayer: boolean;
        airmetLayer: boolean;
    }>>;
}

export function TimeRadio({
    layerVisibility: layerVisibility,
    setLayerVisibility: setLayerVisibility
}: TimeRadioProps) {

    const time_option = ['now', '+1h', '+2h', '+3h', '+4h', '+5h']
    const msl_option = ["ALL", 10, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 360, 420, 450, 480];
    const bulletin_option: (keyof typeof layerVisibility)[] = ['sigmetLayer', 'airmetLayer'];
    const [radioTime, setRadioTime] = useState(0)
    const [mslState, setMslState] = useState(0)
    const [mslOpenWin, setmslOpenWin] = useState(false)
    const [layerOpenWin, setlayerOpenWin] = useState(false)
    const [currentTime, setCurrentTime] = useState('')
    const [currentDate, setCurrentDate] = useState('')

    const popupVariants: Variants = {
        hidden: {
            opacity: 0,
            scale: 0.8,
            height: 0,
            width: 0,
            y: 20,
            x: 0
        },
        visible: {
            opacity: 1,
            scale: 1,
            height: "auto",
            width: "auto",
            y: 0,
            x: 0,
            transition: {
                type: "spring",
                stiffness: 200,
                damping: 15,
                height: { duration: 0.4, ease: "easeOut" },
                width: { duration: 0.3, ease: "easeOut", delay: 0.1 }
            }
        },
        sliceBack: {
            opacity: 1,
            scale: 1.05,
            height: "auto",
            width: "auto",
            y: 0,
            x: 20,
            zIndex: 10,
            transition: {
                type: "spring",
                stiffness: 300,
                damping: 35,
                delay: 0.18,
                // mass: 5
                // duration: 5
            }
        },

        sliceAway: {
            opacity: 1,
            scale: 1,
            height: "auto",
            width: "auto",
            zIndex: 0,
            y: 0,
            x: -15,
            transition: {
                type: "spring",
                stiffness: 350,
                damping: 45,
                delay: 0
            }
        },

        exit: {
            opacity: 0,
            scale: 0.9,
            height: 0,
            width: 0,
            y: 10,
            x: 0,
            transition: {
                duration: 0.25,
                ease: "easeOut",
                opacity: { duration: 0.1 },
                height: { duration: 0.2, delay: 0.05 },
                width: { duration: 0.15, delay: 0.1 }
            }
        }
    };

    const contentVariants: Variants = {
        hidden: {
            opacity: 0,
            height: 0,
            overflow: "hidden"
        },
        visible: (i: number) => ({
            opacity: 1,
            height: "auto",
            transition: {
                delay: i * 0.1,
                duration: 0.3,
                ease: "easeOut"
            }
        })
    };

    useEffect(() => {
        const timer = setInterval(() => {
            const now = new Date();
            now.setHours(now.getHours() + radioTime);

            const timeString = now.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            const dateString = now.toLocaleDateString('en-US', {
                weekday: 'short',
                day: 'numeric',
                month: 'long',
                year: 'numeric'
            });
            setCurrentTime(timeString);
            setCurrentDate(dateString);
        }, 1000);

        return () => clearInterval(timer);
    }, [radioTime]);


    return (
        <div className="flex flex-col items-center relative ">
            <AnimatePresence>
                <div className="absolute left-1/2 transform -translate-x-1/2 bottom-[9.25rem] flex gap-x-4 px-4 py-2">
                    <AnimatePresence>
                        {layerOpenWin && (
                            <motion.div
                                key="layers-popup"
                                initial="hidden"
                                exit="exit"
                                variants={popupVariants}
                                animate={
                                    [
                                        "visible",
                                        mslOpenWin ? "sliceAway" : "sliceBack"
                                    ]
                                }
                                className='
                            rounded-full
                            bg-white/10  
                            ring-1 ring-white/20    
                            min-w-[120px] h-[50px]    
                            items-center
                            justify-center
                            flex-col gap-4
                            backdrop-blur-xs
                            '
                                layout
                                style={{
                                    boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)',
                                    border: '1px solid rgba(255, 255, 255, 0.18)',
                                    // border: '1px solid rgba( 0, 0, 0, 0.25 )',
                                    borderTop: '1px solid rgba( 255, 255, 255, 0.4 )',
                                    borderRight: '1px solid rgba( 255, 255, 255, 0.4 )',
                                    zIndex: 4
                                }}
                            >

                                <motion.div
                                    variants={contentVariants}
                                    initial="hidden"
                                    animate="visible"
                                    custom={0}
                                >
                                    <strong>Layers</strong>
                                </motion.div>
                                <motion.div
                                    variants={contentVariants}
                                    initial="hidden"
                                    animate="visible"
                                    custom={1}
                                    className="flex gap-4 justify-center"
                                >
                                    {bulletin_option.map((val, index) => (
                                        <motion.label key={val} className="flex flex-col items-center"
                                            variants={contentVariants}
                                            initial="hidden"
                                            animate="visible"
                                            custom={2 + index}
                                        >
                                            <motion.input
                                                type="checkbox"
                                                name="bulletin"
                                                checked={layerVisibility[val]}
                                                onChange={() => setLayerVisibility(prev => ({
                                                    ...prev,
                                                    [val]: !prev[val]
                                                }))}
                                                whileHover={{ scale: 1.1 }}
                                                whileTap={{ scale: 0.95 }}
                                            />
                                            <span className="text-[10px] text-white mt-1">{val == 'sigmetLayer' ? 'SIG' : 'AIR'}</span>
                                        </motion.label>
                                    ))}
                                </motion.div>
                            </motion.div>
                        )}
                    </AnimatePresence>
                    <AnimatePresence>
                        {mslOpenWin && (
                            <motion.div
                                key="msl-popup"
                                initial="hidden"
                                exit="exit"
                                variants={popupVariants}
                                animate={
                                    [
                                        "visible",
                                        layerOpenWin ? "sliceAway" : "sliceBack"
                                    ]
                                }
                                custom={0}
                                className="
                            bg-white/10                    
                            rounded-full
                            ring-1 ring-white/20    
                            min-w-[600px] min-h-[50px]    
                            items-center
                            justify-center
                            px-4 py-5
                            flex gap-4
                            backdrop-blur-xs
                            self-end
                        "
                                style={{
                                    boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)',
                                    border: '1px solid rgba(255, 255, 255, 0.18)',
                                    // border: '1px solid rgba( 0, 0, 0, 0.25 )',
                                    borderTop: '1px solid rgba( 255, 255, 255, 0.4 )',
                                    borderRight: '1px solid rgba( 255, 255, 255, 0.4 )',
                                    zIndex: 4
                                }}
                            >
                                {msl_option.map((level, idx) => (
                                    <motion.label key={level} className="flex flex-col items-center" variants={contentVariants} custom={1}>
                                        <motion.input
                                            type="radio"
                                            name="level"
                                            checked={idx === mslState}
                                            onChange={() => setMslState(idx)}
                                            className="w-4 h-4 text-blue-400"
                                            whileHover={{ scale: 1.1 }}
                                            whileTap={{ scale: 0.95 }}
                                        />
                                        <motion.span className="text-xs text-white mt-1" variants={contentVariants} custom={idx}>{level}</motion.span>
                                    </motion.label>
                                ))}
                            </motion.div>
                        )}
                    </AnimatePresence>

                </div>

                <div
                    className="
                        absolute mx-auto
                        bottom-15
                        max-w-[700px]
                        flex items-center justify-between
                        gap-3
                        "
                >
                    <div className='flex gap-3 '>
                        <motion.button
                            className="
                            w-12 h-12
                            !bg-white/10 hover:!bg-white/20
                            backdrop-blur-xs
                            rounded-full
                            ring-1 ring-white/20
                            flex items-center justify-center
                            border-0  focus:outline-none 
                            
                        "
                            style={{

                                border: '1px solid rgba(255, 255, 255, 0.18)',
                                // border: '1px solid rgba( 0, 0, 0, 0.25 )',
                                borderTop: '1px solid rgba( 255, 255, 255, 0.4 )',
                                borderRight: '1px solid rgba( 255, 255, 255, 0.4 )',
                                zIndex: 4
                            }}
                            onClick={() => setlayerOpenWin(!layerOpenWin)}
                            whileHover={{
                                scale: 1.05,
                                backgroundColor: "rgba(255, 255, 255, 0.15)"
                            }}
                            whileTap={{ scale: 0.95 }}
                            animate={{
                                // rotate: layerOpenWin ? 360 : 0,
                                boxShadow: layerOpenWin
                                    ? "0 0 20px rgba(255, 255, 255, 0.3)"
                                    : "0 8px 20px rgba(0, 0, 0, 0.3)"
                            }}
                            transition={{ duration: 0.2 }}
                        >
                            <Layers size={18} className="absolute" />

                        </motion.button>

                        <motion.button
                            onClick={() => setmslOpenWin(!mslOpenWin)}
                            className="
                            w-12 h-12
                            !bg-white/10 hover:!bg-white/20
                            backdrop-blur-xs
                            rounded-full
                            ring-1 ring-white/20
                            flex flex-col items-center justify-center
                            border-0  focus:outline-none 
                             "
                            style={{
                                // boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)',
                                border: '1px solid rgba(255, 255, 255, 0.18)',
                                // border: '1px solid rgba( 0, 0, 0, 0.25 )',
                                borderTop: '1px solid rgba( 255, 255, 255, 0.4 )',
                                borderRight: '1px solid rgba( 255, 255, 255, 0.4 )',
                                zIndex: 4
                            }}
                            whileHover={{
                                scale: 1.05,
                                backgroundColor: "rgba(255, 255, 255, 0.15)"
                            }}
                            whileTap={{ scale: 0.95 }}
                            // Subtle pulse when active
                            animate={{
                                boxShadow: mslOpenWin
                                    ? "0 0 20px rgba(255, 255, 255, 0.3)"
                                    : "0 8px 20px rgba(0, 0, 0, 0.3)"
                            }}
                        >
                            <span className="text-white font-bold text-sm">{msl_option[mslState]}</span>
                            <span className="text-[8px] text-gray-300">MSL</span>
                        </motion.button>
                    </div>

                    <div
                        className="
                            flex items-center justify-center
                            bg-white/10
                            backdrop-blur-xs
                            rounded-full
                            ring-1 ring-white/20
                            min-w-[340px] min-h-[60px] 
                            px-2 py-1
                            gap-3
                        "
                        // style={{ boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)' }}
                        style={{
                            boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)',
                            border: '1px solid rgba(255, 255, 255, 0.18)',
                            // border: '1px solid rgba( 0, 0, 0, 0.25 )',
                            borderTop: '1px solid rgba( 255, 255, 255, 0.4 )',
                            borderRight: '1px solid rgba( 255, 255, 255, 0.4 )',
                            zIndex: 4
                        }}
                    >
                        <motion.button
                            onClick={() => radioTime > 0 && setRadioTime(radioTime - 1)}
                            whileHover={{
                                scale: 1.05,
                                backgroundColor: "rgba(255, 255, 255, 0.15)"
                            }}
                            whileTap={{ scale: 0.95 }}
                            className="
                           w-10 h-10 flex 
                           items-center 
                           justify-center 
                           rounded-full 
                           !bg-white/20 hover:!bg-white/30 
                           text-white 
                           border-0 outline-none focus:outline-none 
                           hover:outline-none active:outline-none 
                           focus-visible:outline-none focus:ring-0 
                           focus-visible:ring-0 shadow-none hover:shadow-none focus:shadow-none
                            "
                            style={{
                                boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)',
                                border: '1px solid rgba(255, 255, 255, 0.18)',
                                // border: '1px solid rgba( 0, 0, 0, 0.25 )',
                                borderTop: '1px solid rgba( 255, 255, 255, 0.4 )',
                                borderRight: '1px solid rgba( 255, 255, 255, 0.4 )',
                                zIndex: 4
                            }}
                        >
                            <ArrowBackIosNewIcon
                                className="text-white"
                                fontSize="small"
                            />
                        </motion.button>

                        <div className="flex items-center gap-4 px-2">
                            {time_option.map((time, idx) => (
                                <label key={time} className="flex flex-col items-center">
                                    <motion.input
                                        type="radio"
                                        name="time"
                                        checked={idx === radioTime}
                                        onChange={() => setRadioTime(idx)}
                                        className="w-4 h-4 text-blue-400"
                                        whileHover={{ scale: 1.1 }}
                                        whileTap={{ scale: 0.95 }}
                                        style={{ boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)' }}
                                    />
                                    <span className="text-[12px] text-white mt-1">{time}</span>
                                </label>
                            ))}
                        </div>

                        <motion.button
                            onClick={() => radioTime < time_option.length - 1 && setRadioTime(radioTime + 1)}
                            whileHover={{
                                scale: 1.05,
                                backgroundColor: "rgba(255, 255, 255, 0.15)"
                            }}
                            whileTap={{ scale: 0.95 }}
                            className="
                            w-10 h-10 flex 
                            items-center 
                            justify-center 
                            rounded-full 
                            !bg-white/20 hover:!bg-white/30 
                            text-white 
                            border-0 outline-none focus:outline-none 
                            hover:outline-none active:outline-none 
                            focus-visible:outline-none focus:ring-0 
                            focus-visible:ring-0 shadow-none hover:shadow-none focus:shadow-none     
                            "
                            // style={{ boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)' }}
                            style={{
                                boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)',
                                border: '1px solid rgba(255, 255, 255, 0.18)',
                                // border: '1px solid rgba( 0, 0, 0, 0.25 )',
                                borderTop: '1px solid rgba( 255, 255, 255, 0.4 )',
                                borderRight: '1px solid rgba( 255, 255, 255, 0.4 )',
                                zIndex: 4
                            }}
                        >
                            <ArrowForwardIosIcon className="text-white" fontSize="small" />
                        </motion.button>
                    </div>


                    <div
                        className="
                        bg-white/10
                        backdrop-blur-xs
                        rounded-full
                        ring-1 ring-white/20
                        px-4 py-2
                        flex flex-col items-center
                        min-w-[8rem]
                    "
                        style={{
                            boxShadow: '0 8px 20px rgba(0, 0, 0, 0.3)',
                            border: '1px solid rgba(255, 255, 255, 0.18)',
                            // border: '1px solid rgba( 0, 0, 0, 0.25 )',
                            borderTop: '1px solid rgba( 255, 255, 255, 0.4 )',
                            borderRight: '1px solid rgba( 255, 255, 255, 0.4 )',
                            zIndex: 4
                        }}
                    >
                        <span className="text-[10px] text-gray-300">{currentDate}</span>
                        <span className="text-lg text-white font-bold">{currentTime}</span>
                        <span className="text-[10px] text-gray-300">UTC+7 (+{radioTime}h)</span>
                    </div>
                </div>
            </AnimatePresence>
        </div>
    )
}